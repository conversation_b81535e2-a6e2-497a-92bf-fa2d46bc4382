import { Paginator } from '@/types/paginator.ts';

export type JobFormType = {
  recruitment_type: string;
  employer_email: string;
  employer_name: string;
  employer_phone_number: string;
  category_id: number;
  type: string;
  is_public: boolean;
  is_instant: boolean;
  thumbnail: File | null;
  images: File[];
  title: string;
  description: string;
  benefits?: string;
  time_start?: string;
  time_end?: string;
  age?: number;
  gender?: string;
  quantity?: number;
  certificate_level?: string;
  prefecture: string;
  address: string;
  salary_type: string;
  salary?: number;
  travel_fee_type: string;
  travel_fee?: number;
  expired_at: string;
  job_start_at: string;
  // Image management fields for edit mode
  deleted_image_ids?: number[];
  existing_images?: Images[];
};

export type JobType = {
  id: number;
  recruitmentType: string;
  employerEmail: string;
  employerName: string;
  employerPhoneNumber: string;
  categoryId: number;
  categoryName: string;
  type: string;
  typeName: string;
  isPublic: boolean;
  isInstant: boolean;
  thumbnailId: number;
  thumbnailUrl: string;
  title: string;
  description: string;
  benefits?: string;
  timeStart?: string;
  timeEnd?: string;
  age?: number;
  gender?: string;
  quantity?: number;
  certificateLevel: string;
  prefecture: string;
  address: string;
  salaryType: string;
  salary?: number;
  travelFeeType: string;
  travelFee?: number;
  expiredAt: string;
  jobStartAt: string;
  createdAt: string;
  updatedAt: string;
  images: Images[];
};

type Images = {
  id: number;
  imageUrl: string;
};

export type JobCategoryType = {
  id: number;
  name: string;
};

export type ListJobProps = {
  jobs: {
    data: JobType[];
    paginator: Paginator;
  };
  categories: JobCategoryType[];
};

export type DetailJobProps = {
  job: JobType;
};

export type EditJobProps = DetailJobProps & {
  categories: JobCategoryType[];
};

export type CreateJobProps = {
  categories: JobCategoryType[];
};
