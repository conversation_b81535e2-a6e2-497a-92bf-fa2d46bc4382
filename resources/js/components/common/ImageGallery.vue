<script setup lang="ts">
import { ref, computed } from 'vue';
import { TrashIcon, EyeIcon } from '@heroicons/vue/24/outline';
import { useI18n } from '@/composables/useI18n.ts';

interface ImageItem {
  id: number;
  imageUrl: string;
  name?: string;
}

interface Props {
  images: ImageItem[];
  title?: string;
  deletedImageIds?: number[];
  readonly?: boolean;
  showPreview?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  deletedImageIds: () => [],
  readonly: false,
  showPreview: true,
});

const emit = defineEmits<{
  (e: 'delete', imageId: number): void;
  (e: 'preview', imageUrl: string): void;
}>();

const { t } = useI18n();
const previewImage = ref<string | null>(null);

const visibleImages = computed(() => {
  return props.images.filter(image => !props.deletedImageIds.includes(image.id));
});

const handleDelete = (imageId: number) => {
  if (props.readonly) return;
  
  if (confirm(t('common.confirm.delete'))) {
    emit('delete', imageId);
  }
};

const handlePreview = (imageUrl: string) => {
  if (props.showPreview) {
    previewImage.value = imageUrl;
    emit('preview', imageUrl);
  }
};

const closePreview = () => {
  previewImage.value = null;
};
</script>

<template>
  <div class="w-full">
    <label v-if="title" class="block text-sm font-medium text-gray-700 mb-3">
      {{ title }}
    </label>
    
    <div v-if="visibleImages.length > 0" class="border rounded-lg p-4 bg-gray-50">
      <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        <div 
          v-for="image in visibleImages" 
          :key="image.id" 
          class="relative group border rounded-lg p-2 bg-white hover:shadow-md transition-shadow"
        >
          <img 
            :src="image.imageUrl" 
            :alt="image.name || `Image ${image.id}`" 
            class="w-full h-32 object-cover rounded cursor-pointer"
            @click="handlePreview(image.imageUrl)"
          />
          
          <!-- Image overlay with actions -->
          <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
            <div class="flex space-x-2">
              <button
                v-if="showPreview"
                @click="handlePreview(image.imageUrl)"
                class="p-2 bg-white rounded-full shadow-lg hover:bg-gray-100 transition-colors"
                :title="t('common.btn.preview')"
              >
                <EyeIcon class="w-4 h-4 text-gray-600" />
              </button>
              
              <button
                v-if="!readonly"
                @click="handleDelete(image.id)"
                class="p-2 bg-red-500 rounded-full shadow-lg hover:bg-red-600 transition-colors"
                :title="t('common.btn.delete')"
              >
                <TrashIcon class="w-4 h-4 text-white" />
              </button>
            </div>
          </div>
          
          <!-- Image name/info -->
          <p class="text-xs text-gray-500 text-center mt-1 truncate">
            {{ image.name || `Image ${image.id}` }}
          </p>
        </div>
      </div>
      
      <p class="text-sm text-gray-600 text-center mt-3">
        {{ visibleImages.length }} {{ t('models/job.field.images').toLowerCase() }}
        <span v-if="props.deletedImageIds.length > 0" class="text-red-500">
          ({{ props.deletedImageIds.length }} {{ t('common.status.marked_for_deletion') }})
        </span>
      </p>
    </div>
    
    <div v-else class="border rounded-lg p-8 bg-gray-50 text-center text-gray-500">
      <svg class="mx-auto h-12 w-12 text-gray-400 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>
      {{ t('common.message.no_images') }}
    </div>

    <!-- Image Preview Modal -->
    <div 
      v-if="previewImage" 
      class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75"
      @click="closePreview"
    >
      <div class="relative max-w-4xl max-h-full p-4">
        <img 
          :src="previewImage" 
          alt="Preview" 
          class="max-w-full max-h-full object-contain rounded-lg"
        />
        <button
          @click="closePreview"
          class="absolute top-2 right-2 p-2 bg-white rounded-full shadow-lg hover:bg-gray-100"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Custom scrollbar for better UX */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
