<script setup lang="ts">
import { EditJobProps, JobFormType } from '@/types/job.ts';
import { useI18n } from '@/composables/useI18n.ts';
import { ref } from 'vue';
import { Head, InertiaForm, useForm } from '@inertiajs/vue3';
import { route } from 'ziggy-js';
import JobForm from '@/pages/Job/common/JobForm.vue';
import Modal from '@/components/common/Modal.vue';

const props = defineProps<EditJobProps>();

const { t } = useI18n();
const isPreviewEdit = ref(false);

const formData = useForm<JobFormType>({
  recruitment_type: props.job.recruitmentType,
  employer_email: props.job.employerEmail,
  employer_name: props.job.employerName,
  employer_phone_number: props.job.employerPhoneNumber,
  category_id: props.job.categoryId,
  type: props.job.type,
  is_public: props.job.isPublic,
  is_instant: props.job.isInstant,
  thumbnail: null,
  images: [],
  title: props.job.title,
  description: props.job.description,
  benefits: props.job.benefits || '',
  time_start: props.job.timeStart || '',
  time_end: props.job.timeEnd || '',
  age: props.job.age,
  gender: props.job.gender,
  quantity: props.job.quantity,
  certificate_level: props.job.certificateLevel,
  prefecture: props.job.prefecture,
  address: props.job.address,
  salary_type: props.job.salaryType,
  salary: props.job.salary,
  travel_fee_type: props.job.travelFeeType,
  travel_fee: props.job.travelFee,
  expired_at: props.job.expiredAt,
  job_start_at: props.job.jobStartAt,
});

const update = (form: InertiaForm<JobFormType>) => {
  form.put(route('admin.job.update', props.job.id), {
    onSuccess: () => {
      isPreviewEdit.value = false;
    },
  });
};
</script>

<template>
  <Head :title="t('models/job.screenName.edit')" />
  <div class="flex flex-wrap items-center justify-between gap-3 mb-6">
    <h2 class="text-xl font-semibold text-gray-800">
      {{ t('models/job.screenName.edit') }}
    </h2>
  </div>
  <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6">
    <JobForm :form-data="formData" :categories="categories" @openPreview="isPreviewEdit = true" />
  </div>
  <Modal v-if="isPreviewEdit" @close="isPreviewEdit = false" :title="t('models/job.screenName.edit')">
    <template #body>
      <JobForm
        :form-data="formData"
        :categories="categories"
        @submit="update"
        @openPreview="isPreviewEdit = true"
        @close="isPreviewEdit = false"
        :is-preview="true"
      />
    </template>
  </Modal>
</template>

<style scoped></style>
