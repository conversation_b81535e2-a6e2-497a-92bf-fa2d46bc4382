<?php

namespace Src\Domain\Admin\Requests\Job;

use Src\Domain\Admin\Models\Job\JobForm;
use Src\Domain\Admin\Requests\FormRequest;

class BaseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Rules
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'recruitment_type' => [
                'required',
                'string',
                'max:20'
            ],
            'employer_email' => [
                'required',
                'email',
                'max:50'
            ],
            'employer_name' => [
                'required',
                'string',
                'max:255'
            ],
            'employer_phone_number' => [
                'required',
                'string',
                'max:11'
            ],
            'category_id' => [
                'required',
                'integer',
                'exists:m_job_categories,id'
            ],
            'type' => [
                'required',
                'string',
                'max:20'
            ],
            'is_public' => [
                'boolean'
            ],
            'is_instant' => [
                'boolean'
            ],
            'title' => [
                'required',
                'string',
                'max:1000'
            ],
            'description' => [
                'required',
                'string'
            ],
            'benefits' => [
                'nullable',
                'string'
            ],
            'time_start' => [
                'required',
            ],
            'time_end' => [
                'required',
                'after:time_start'
            ],
            'age' => [
                'nullable',
                'integer',
                'min:16',
                'max:100'
            ],
            'gender' => [
                'nullable',
                'string',
                'max:10'
            ],
            'quantity' => [
                'nullable',
            ],
            'certificate_level' => [
                'required',
                'string',
                'max:10'
            ],
            'prefecture' => [
                'required',
                'string',
                'max:255'
            ],
            'address' => [
                'required',
                'string',
                'max:255'
            ],
            'salary_type' => [
                'required',
                'string',
                'max:10'
            ],
            'salary' => [
                'nullable',
                'integer',
                'min:0'
            ],
            'travel_fee_type' => [
                'required',
                'string',
                'max:10'
            ],
            'travel_fee' => [
                'nullable',
                'integer',
                'min:0'
            ],
            'expired_at' => [
                'required',
                'date',
                'after:today'
            ],
            'job_start_at' => [
                'required',
                'date'
            ]
        ];
    }

    /**
     * Attributes
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'recruitment_type' => __('models/job.field.recruitment_type'),
            'employer_email' => __('models/job.field.employer_email'),
            'employer_name' => __('models/job.field.employer_name'),
            'employer_phone_number' => __('models/job.field.employer_phone_number'),
            'category_id' => __('models/job.field.category_id'),
            'type' => __('models/job.field.type'),
            'is_public' => __('models/job.field.is_public'),
            'is_instant' => __('models/job.field.is_instant'),
            'thumbnail' => __('models/job.field.thumbnail'),
            'images' => __('models/job.field.images'),
            'title' => __('models/job.field.title'),
            'description' => __('models/job.field.description'),
            'benefits' => __('models/job.field.benefits'),
            'time_start' => __('models/job.field.time_start'),
            'time_end' => __('models/job.field.time_end'),
            'age' => __('models/job.field.age'),
            'gender' => __('models/job.field.gender'),
            'quantity' => __('models/job.field.quantity'),
            'certificate_level' => __('models/job.field.certificate_level'),
            'prefecture' => __('models/job.field.prefecture'),
            'address' => __('models/job.field.address'),
            'salary_type' => __('models/job.field.salary_type'),
            'salary' => __('models/job.field.salary'),
            'travel_fee_type' => __('models/job.field.travel_fee_type'),
            'travel_fee' => __('models/job.field.travel_fee'),
            'expired_at' => __('models/job.field.expired_at'),
            'job_start_at' => __('models/job.field.job_start_at'),
        ];
    }

    /**
     * Get the validated form data.
     */
    public function validatedForm(): JobForm
    {
        return new JobForm($this->validated());
    }
}
